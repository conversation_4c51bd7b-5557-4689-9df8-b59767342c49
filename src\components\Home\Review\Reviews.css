.reviews {
    position: relative;
    width: 100vw;
    height: 100svh;
    background-color: var(--fg);
    color: var(--bg);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    gap: 2em;
}

.review-box {
    position: relative;
    width: 97vw;
    height: 90svh;
    background-color: var(--fg);
    color: var(--bg);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: dashed 1px #ffffff;
    gap: 2em;
    border-radius: 50px;
}

#review-copy {
    width: 50%;
    margin: 0 auto;
}

.review-item {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 2em;

    font-size: 2rem;
    font-style: italic;
}

#quote-icon {
    position: absolute;
    top: 25%;
    font-size: 2.2rem;
    color: var(--bg200);
}

.reviews-list {
    position: absolute;
    bottom: 10%;
    width: 22%;
    margin: 0 auto;
    display: flex;
    gap: 2rem;
}

.review-thumbnail {
    aspect-ratio: 1/1;
    min-height: 50px;
    border-radius: 0.5em;
    overflow: hidden;
    cursor: pointer;
    transition: border 0.3s ease-in-out;
}

.review-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: fill;
    object-position: bottom;
}

.review-thumbnail.active {
    padding: 4px;
    border: 1px dashed var(--bg);
}

h4#review-copy,
h4#review-author {
    font-kerning: none;

    overflow: hidden;
    line-height: 1 !important;
}

h4#review-copy .line,
h4#review-author .line {
    position: relative;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
    overflow: hidden;
}

h4#review-copy .line span,
h4#review-author .line span {
    position: relative;
    display: inline-block;
    will-change: transform;
}

/* Trusted Hospitals Section */
.trusted-hospitals {
    width: 100vw;
    margin-top: 1em;
    padding: 2em;
    padding-top: 8em;
    padding-bottom: 0;
    background-color: #121214;
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.trusted-hospitals-header {
    text-align: center;
}

.trusted-hospitals-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    color: white;
    font-weight: bold;
    margin: 0px;
    margin-bottom: 40px;
    font-style: italic;
}

.hospitals-grid {
    display: flex;
    gap: 1em;
    width: 100%;
}

.hospital {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    aspect-ratio: 1;
    border: 1px dashed white;
    border-radius: 1em;
    background-color: transparent;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hospital::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: var(--bg-image);
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.hospital.hovered::before {
    opacity: 1;
}

.hospital-name {
    font-size: clamp(0.9rem, 1.5vw, 1.1rem);
    color: white;
    font-weight: bold;
    margin: 0;
    line-height: 1.2;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 2;
    transition: color 0.3s ease;
}

.hospital:hover .hospital-name {
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Animation Classes for GSAP */
.hospital-name {
    will-change: transform, opacity;
}

@media (max-width: 1000px) {
    .trusted-hospitals {
        padding: 1.25em;
        padding-bottom: 0;
        gap: 1.25em;
    }

    .hospitals-grid {
        flex-direction: column;
        gap: 1.25em;
    }

    .hospital {
        aspect-ratio: 5/3;
    }

    .hospital-name {
        font-size: clamp(1rem, 2vw, 1.2rem);
    }

    .reviews {
        padding: 4em 1.25em;
    }

    #review-copy {
        width: 100%;
    }

    .reviews-list {
        width: 50%;
    }

    .review-item {
        padding: 1.25em;
    }
}