.footer {
  width: 100vw;
  height: 100%;
  min-height: 100svh;
  padding: 4em 2em 2em 2em;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  background-color: var(--fg);
  color: var(--bg);
}

.footer-row:nth-child(1) {
  display: flex;
  justify-content: space-between;
  gap: 4em;
}

.footer-contact {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5em;
}

.footer-contact h3 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--bg);
  font-style: italic;
}

.footer-contact h3 span {
  color: var(--accent1);
}

.footer-contact p {
  margin-bottom: 0.5em;
  color: var(--accent1);
  font-size: 1.1rem;
  line-height: 1.5;
}

.footer-contact .btn {
  display: inline-block;
  padding: 1em 2em;
  border: 1px dashed var(--bg);
  border-radius: 0.5em;
  background-color: transparent;
  color: var(--bg);
  text-decoration: none;
  font-family: 'Rader', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  width: fit-content;
  font-style: italic;
}

.footer-contact .btn:hover {
  background-color: var(--bg);
  color: var(--fg);
}

.footer-nav {
  flex: 2;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2em;
}

.footer-nav-section {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}

.footer-nav-section h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--bg);
  margin-bottom: 1em;
  padding-bottom: 0.5em;
  border-bottom: 1px dashed var(--accent1);
}

.footer-nav-item {
  padding: 0.5em 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-decoration: none;
  color: var(--accent1);
  font-size: 0.9rem;
  transition: color 0.3s ease;
  border-top: 1px dashed transparent;
}

.footer-nav-item:hover {
  color: var(--bg);
  border-top: 1px dashed var(--accent1);
}

.footer-nav-item span:last-child {
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(0);
}

.footer-nav-item:hover span:last-child {
  opacity: 1;
  transform: translateX(5px);
}

.footer-header {
  width: 100%;
  padding: 4em 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.footer-header h1 {
  position: relative;
  left: -0.5vw;
  font-size: 15vw;
  color: var(--bg);
  font-weight: 700;
  font-style: italic;
}

.footer-copyright-line {
  width: 100%;
  padding: 0.5em 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px dashed var(--accent1);
}

.footer-copyright-line p {
  color: var(--accent1);
  font-size: 0.9rem;
}

.footer-demo-link {
  color: var(--bg);
  text-decoration: none;
  font-weight: 500;
  font-style: italic;
  transition: color 0.3s ease;
}

.footer-demo-link:hover {
  color: var(--accent1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .footer-nav {
    grid-template-columns: repeat(2, 1fr);
    gap: 3em;
  }
}

@media (max-width: 1000px) {
  .footer-row:nth-child(1) {
    flex-direction: column;
    gap: 3em;
  }

  .footer-nav {
    grid-template-columns: repeat(2, 1fr);
    gap: 2em;
  }

  .footer-header {
    padding: 2em 0;
  }

  .footer {
    padding: 4em 1.25em 1.25em 1.25em;
  }

  .footer-contact h3 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .footer-nav {
    grid-template-columns: 1fr;
    gap: 2em;
  }

  .footer-header h1 {
    font-size: 20vw;
  }

  .footer-contact h3 {
    font-size: 1.8rem;
  }

  .footer-copyright-line {
    flex-direction: column;
    gap: 1em;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 3em 1em 1em 1em;
  }

  .footer-contact h3 {
    font-size: 1.5rem;
  }

  .footer-contact p {
    font-size: 1rem;
  }

  .footer-header {
    padding: 1em 0;
  }

  .footer-header h1 {
    font-size: 25vw;
  }
}