import React, { useEffect, useState } from 'react';
import './Navbar.css';

const Navbar = () => {
  const [scrollDirection, setScrollDirection] = useState(null);
  const [isAtTop, setIsAtTop] = useState(true);

  useEffect(() => {
    let lastScrollY = window.pageYOffset;

    const updateScrollDirection = () => {
      // Check if we're in horizontal scroll section - if so, don't update anything
      const navbar = document.querySelector('.navbar');
      if (navbar && navbar.classList.contains('navbar-horizontal-hidden')) {
        return; // Exit early, don't update any scroll-based state
      }

      const scrollY = window.pageYOffset;

      // Check if we're at the top
      setIsAtTop(scrollY <= 10);

      // Determine scroll direction with threshold to prevent jittery behavior
      if (Math.abs(scrollY - lastScrollY) > 10) {
        const direction = scrollY > lastScrollY ? "down" : "up";
        if (direction !== scrollDirection) {
          setScrollDirection(direction);
        }
        lastScrollY = scrollY > 0 ? scrollY : 0;
      }
    };

    window.addEventListener('scroll', updateScrollDirection);

    return () => {
      window.removeEventListener('scroll', updateScrollDirection);
    };
  }, [scrollDirection]);

  // Check if navbar should be hidden by horizontal scroll (takes priority)
  const isHorizontallyHidden = React.useRef(false);

  React.useEffect(() => {
    const checkHorizontalState = () => {
      const navbar = document.querySelector('.navbar');
      isHorizontallyHidden.current = navbar && navbar.classList.contains('navbar-horizontal-hidden');
    };

    // Check immediately and set up mutation observer
    checkHorizontalState();

    const observer = new MutationObserver(checkHorizontalState);
    const navbar = document.querySelector('.navbar');
    if (navbar) {
      observer.observe(navbar, { attributes: true, attributeFilter: ['class'] });
    }

    return () => observer.disconnect();
  }, []);

  // Determine if navbar should be hidden (only apply normal logic if not horizontally hidden)
  const shouldHideNavbar = !isHorizontallyHidden.current && !isAtTop && scrollDirection === "down";

  return (
    <nav className={`navbar ${shouldHideNavbar ? 'navbar-hidden' : ''}`}>
      {/* Backdrop blur div with custom clip-path */}
      <div className="navbar-backdrop"></div>

      {/* SVG Background with Notched Design and Clip Path Definition */}
      <svg
        className="navbar-bg"
        width="1402"
        height="96"
        viewBox="0 0 1402 96"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="none"
      >
        <defs>
          <clipPath id="navbar-clip" clipPathUnits="objectBoundingBox">
            <path d="M1 0C1.0004 0 1 0.0104 1 0.0104V0.3576C1 0.5589 0.9881 0.7222 0.9757 0.7222H0.6717C0.6616 0.7222 0.6532 0.8187 0.6453 0.9053C0.6395 0.9643 0.6331 1 0.6258 1H0.3693C0.3619 1 0.3555 0.9643 0.3498 0.9053C0.3419 0.8187 0.3335 0.7222 0.3234 0.7222H0.025C0.0112 0.7222 0 0.5589 0 0.3576V0.0104C0 0.0104 -0.0004 0 0 0H1Z" />
          </clipPath>
          <filter id="filter0_n_13_43" x="0" y="0" width="1402" height="96" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feTurbulence type="fractalNoise" baseFrequency="0.625 0.625" stitchTiles="stitch" numOctaves="3" result="noise" seed="3830" />
            <feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
            <feComponentTransfer in="alphaNoise" result="coloredNoise1">
              <feFuncA type="discrete" tableValues="1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 " />
            </feComponentTransfer>
            <feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
            <feFlood floodColor="rgba(0, 0, 0, 0.25)" result="color1Flood" />
            <feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
            <feMerge result="effect1_noise_13_43">
              <feMergeNode in="shape" />
              <feMergeNode in="color1" />
            </feMerge>
          </filter>
        </defs>
        <g filter="url(#filter0_n_13_43)">
          <path d="M1401 0C1401.55 1.66858e-05 1402 0.447726 1402 1V34.333C1402 53.663 1386.33 69.333 1367 69.333H941.237C927.428 69.333 915.564 78.5798 904.552 86.9106C897.011 92.6158 887.616 96 877.431 96H517.431C507.245 96 497.851 92.6158 490.309 86.9106C479.297 78.5798 467.433 69.333 453.625 69.333H35C15.67 69.333 0 53.663 0 34.333V1C0 0.447715 0.447715 1.49739e-08 1 0H1401ZM878.515 92.9863L879.575 92.9463C879.58 92.9461 879.584 92.9455 879.589 92.9453C879.232 92.9634 878.874 92.9772 878.515 92.9863Z" fill="#233F5F" />
        </g>
      </svg>



      {/* Navbar Content */}
      <div className="navbar-content">
        {/* Left Section */}
        <div className="navbar-left">
          <a href="#" className="nav-link">Product</a>
          <a href="#" className="nav-link">Solutions</a>
          <a href="#" className="nav-link">Request Demo</a>
        </div>

        {/* Center Section - Logo */}
        <div className="navbar-center">
          <div className="logo-container">
            <span className="logo-text">AIRHS</span>

          </div>
        </div>

        {/* Right Section */}
        <div className="navbar-right">
          <a href="#" className="nav-link">About</a>
          <a href="#" className="nav-link">Blog</a>
          <a href="#" className="nav-link">Contact</a>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
