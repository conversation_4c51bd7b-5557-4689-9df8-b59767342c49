import React from 'react';
import './Home.css';
import Navbar from './Navbar/Navbar';
import <PERSON> from './Hero/Hero.jsx';
import PatientCareSection from './PatientCareSection/PatientCareSection.jsx';
import HorizontalScroll from './HorizontalScroll/HorizontalScroll.jsx';
import ProblemSol from './ProblemSol/ProblemSol.jsx';
import ProcessCards from './KeyFeatures/ProcessCards.jsx';
import FoundersEdge from './FoundersEdge/FoundersEdge.jsx';
import Reviews from './Review/Reviews.jsx';
import ContactForm from '../ContactForm/ContactForm.jsx';
import Footer from '../Footer/Footer.jsx';

const Home = () => {
  return (
    <div className="page home-page">
      <Navbar />
      <Hero />
      <PatientCareSection />
      <HorizontalScroll />
      <ProblemSol />
      <ProcessCards />
      <FoundersEdge />
      <Reviews />
      <ContactForm />
      <Footer />
    </div>
  );
};

export default Home;
