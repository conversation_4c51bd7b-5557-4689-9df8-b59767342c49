import React from "react";
import "./Footer.css";
import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <div className="footer">
      <div className="footer-row">
        <div className="footer-contact">
          <h3>
            Let's Transform Healthcare <br />
            contact<span>@</span>airhs.com
          </h3>

          <p className="secondary">
            From small clinics to large hospital networks — we're here to revolutionize 
            your healthcare operations with AI-powered solutions.
          </p>

          <Link to="/contact" className="btn">
            Book a Demo →
          </Link>
        </div>

        <div className="footer-nav">
          <div className="footer-nav-section">
            <h4>Product</h4>
            <Link to="/ai-scheduling" className="footer-nav-item">
              <span>AI Scheduling</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/patient-management" className="footer-nav-item">
              <span>Patient Management</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/billing-automation" className="footer-nav-item">
              <span>Billing Automation</span>
              <span>&#8594;</span>
            </Link>
          </div>

          <div className="footer-nav-section">
            <h4>Company</h4>
            <Link to="/about" className="footer-nav-item">
              <span>About</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/founders" className="footer-nav-item">
              <span>Founders</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/careers" className="footer-nav-item">
              <span>Careers</span>
              <span>&#8594;</span>
            </Link>
          </div>

          <div className="footer-nav-section">
            <h4>Legal</h4>
            <Link to="/privacy" className="footer-nav-item">
              <span>Privacy Policy</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/terms" className="footer-nav-item">
              <span>Terms of Service</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/compliance" className="footer-nav-item">
              <span>HIPAA Compliance</span>
              <span>&#8594;</span>
            </Link>
          </div>

          <div className="footer-nav-section">
            <h4>Contact</h4>
            <Link to="/support" className="footer-nav-item">
              <span>Support</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/demo" className="footer-nav-item">
              <span>Request Demo</span>
              <span>&#8594;</span>
            </Link>
            <Link to="/contact" className="footer-nav-item">
              <span>Get in Touch</span>
              <span>&#8594;</span>
            </Link>
          </div>
        </div>
      </div>
      
      <div className="footer-row">
        <div className="footer-header">
          <h1>AIR</h1>
          <h1>HS</h1>
        </div>

        <div className="footer-copyright-line">
          <p className="primary sm">&copy; AIR-HS 2024</p>
          <Link to="/demo" className="footer-demo-link">
            Book a Demo →
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Footer;
