/* Navbar Component Styles */
.navbar {
  position: fixed;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 60px;
  z-index: 1000;
  pointer-events: none;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.navbar-hidden {
  transform: translateX(-50%) translateY(-100%);
  opacity: 0;
}

.navbar-horizontal-hidden {
  transform: translateX(-50%) translateY(-100%) !important;
  opacity: 0 !important;
  pointer-events: none !important;
  visibility: hidden !important;
}

/* Backdrop blur with custom shape */
.navbar-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  clip-path: url(#navbar-clip);
}

/* SVG Background */
.navbar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Navbar Content Container */
.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 20px 80px 20px;
  pointer-events: auto;
}

/* Left Section */
.navbar-left {
  display: flex;
  align-items: center;

  gap: 80px;
  flex: 1;
}

/* Center Section */
.navbar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-text {
  font-family: 'Rader', sans-serif;
  font-size: 52px;
  font-weight: 700;
  color: #f7f7f2;
  letter-spacing: 4px;
  padding-top: 20px;
  padding-left: 20px;
  padding-bottom: 20px;
}

.logo-icon {
  color: #f7f7f2;
  display: flex;
  align-items: center;
}

/* Right Section */
.navbar-right {
  display: flex;
  align-items: center;
  gap: 80px;
  flex: 1;
  justify-content: flex-end;
}

/* Navigation Links */
.nav-link {
  font-family: 'Rader', sans-serif;
  font-size: 20px;
  font-weight: 400;
  color: #f7f7f2;
  text-decoration: none;
  transition: color 0.2s ease;
  cursor: pointer;
  padding-bottom: 17px;
}



/* Responsive Design */
@media (max-width: 1024px) {
  .navbar {
    width: 95%;
    height: 70px;
  }

  .navbar-content {
    padding: 0 30px;
  }

  .navbar-left,
  .navbar-right {
    gap: 24px;
  }

  .logo-text {
    font-size: 20px;
  }

  .nav-link {
    font-size: 14px;

  }
}

@media (max-width: 768px) {
  .navbar {
    width: 98%;
    height: 60px;
  }

  .navbar-content {
    padding: 0 20px;
  }

  .navbar-left,
  .navbar-right {
    gap: 16px;
  }

  .logo-text {
    font-size: 18px;
  }

  .nav-link {
    font-size: 13px;
  }
}

@media (max-width: 640px) {

  .navbar-left .nav-link:nth-child(2),
  .navbar-right .nav-link:nth-child(2) {
    display: none;
  }

  .navbar-left,
  .navbar-right {
    gap: 12px;
  }
}